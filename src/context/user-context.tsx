import React, { createContext, useContext, useEffect, useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import type { User, UserPermissions } from '@/types/user'
import { getCurrentUser, getCachedUser, clearUserData } from '@/services/user'
import { getUserPermissions } from '@/types/user'

interface UserContextType {
  user: User | null
  permissions: UserPermissions
  isLoading: boolean
  error: string | null
  refreshUser: () => Promise<void>
  logout: () => void
  isAuthenticated: boolean
}

const UserContext = createContext<UserContextType | undefined>(undefined)

interface UserProviderProps {
  children: React.ReactNode
}

export function UserProvider({ children }: UserProviderProps) {
  const [user, setUser] = useState<User | null>(getCachedUser())
  const queryClient = useQueryClient()

  // Query to fetch user data
  const {
    data: fetchedUser,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['currentUser'],
    queryFn: getCurrentUser,
    enabled: !!localStorage.getItem('token') && !user, // Only fetch if we have a token and no cached user
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error.message.includes('Token has expired') || error.message.includes('No authentication token')) {
        return false
      }
      return failureCount < 2
    },
    onError: (error) => {
      console.error('Failed to fetch user data:', error)
      // Clear user data on auth errors
      if (error.message.includes('Token has expired') || error.message.includes('No authentication token')) {
        handleLogout()
      }
    },
  })

  // Update user state when query data changes
  useEffect(() => {
    if (fetchedUser) {
      setUser(fetchedUser)
    }
  }, [fetchedUser])

  // Initialize user from cache on mount
  useEffect(() => {
    const cachedUser = getCachedUser()
    if (cachedUser) {
      setUser(cachedUser)
    }
  }, [])

  const refreshUser = async () => {
    try {
      await refetch()
    } catch (error) {
      console.error('Failed to refresh user data:', error)
    }
  }

  const handleLogout = () => {
    clearUserData()
    setUser(null)
    queryClient.clear()
    window.location.href = '/sign-in-2'
  }

  const permissions = getUserPermissions(user)
  const isAuthenticated = !!user && !!localStorage.getItem('token')

  const value: UserContextType = {
    user,
    permissions,
    isLoading,
    error: error?.message || null,
    refreshUser,
    logout: handleLogout,
    isAuthenticated,
  }

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>
}

export const useUser = () => {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}

// Convenience hooks
export const useUserPermissions = () => {
  const { permissions } = useUser()
  return permissions
}

export const useIsAdmin = () => {
  const { user } = useUser()
  return user?.isAdmin ?? false
}

export const useIsConsultant = () => {
  const { user } = useUser()
  return user?.isConsultant ?? false
}

export const useUserProfile = () => {
  const { user } = useUser()
  return user?.consultant || null
}
