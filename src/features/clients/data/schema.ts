import { z } from 'zod'

// API-compliant client schema matching the backend structure
export const clientSchema = z.object({
  id: z.number(),
  name: z.string().min(1, 'Client name is required'),
  code: z.string().min(1, 'Client code is required'),
  description: z.string().optional(),
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

// Form schemas for creating/editing clients
export const clientFormSchema = z.object({
  name: z.string().min(1, 'Client name is required').max(100, 'Name must be less than 100 characters'),
  code: z.string()
    .min(2, 'Client code must be at least 2 characters')
    .max(10, 'Client code must be less than 10 characters')
    .regex(/^[A-Z0-9]+$/, 'Client code must contain only uppercase letters and numbers'),
  description: z.string().optional(),
  contactEmail: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  isActive: z.boolean().default(true),
})

// List schema for parsing arrays of clients
export const clientListSchema = z.array(clientSchema)

// Type exports
export type Client = z.infer<typeof clientSchema>
export type ClientForm = z.infer<typeof clientFormSchema>
export type ClientList = z.infer<typeof clientListSchema>

// API request/response types
export interface CreateClientRequest {
  name: string
  code: string
  description?: string
  contactEmail?: string
  contactPhone?: string
  address?: string
  isActive?: boolean
}

export interface UpdateClientRequest extends Partial<CreateClientRequest> {
  id: number
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  error: string
  success: boolean
  message?: string
}
