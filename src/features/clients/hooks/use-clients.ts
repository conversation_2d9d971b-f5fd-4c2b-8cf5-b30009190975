import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/hooks/use-toast'
import {
  getAllClients,
  getClientById,
  createClient,
  updateClient,
  deleteClient,
  getActiveClients,
  searchClients,
  toggleClientStatus,
  handleClientError,
} from '@/services/clients'
import type {
  Client,
  CreateClientRequest,
  UpdateClientRequest,
} from '../data/schema'

// Query keys
export const clientKeys = {
  all: ['clients'] as const,
  lists: () => [...clientKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...clientKeys.lists(), { filters }] as const,
  details: () => [...clientKeys.all, 'detail'] as const,
  detail: (id: number) => [...clientKeys.details(), id] as const,
  active: () => [...clientKeys.all, 'active'] as const,
  search: (query: string) => [...clientKeys.all, 'search', query] as const,
}

/**
 * Hook to get all clients
 */
export const useClients = () => {
  return useQuery({
    queryKey: clientKeys.lists(),
    queryFn: getAllClients,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get a specific client by ID
 */
export const useClient = (id: number) => {
  return useQuery({
    queryKey: clientKeys.detail(id),
    queryFn: () => getClientById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get active clients only
 */
export const useActiveClients = () => {
  return useQuery({
    queryKey: clientKeys.active(),
    queryFn: getActiveClients,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to search clients
 */
export const useSearchClients = (query: string) => {
  return useQuery({
    queryKey: clientKeys.search(query),
    queryFn: () => searchClients(query),
    enabled: query.length >= 2, // Only search if query is at least 2 characters
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to create a new client
 */
export const useCreateClient = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createClient,
    onSuccess: (newClient) => {
      // Invalidate and refetch clients list
      queryClient.invalidateQueries({ queryKey: clientKeys.lists() })
      queryClient.invalidateQueries({ queryKey: clientKeys.active() })

      // Add the new client to the cache
      queryClient.setQueryData(clientKeys.detail(newClient.id), newClient)

      toast({
        title: 'Client created',
        description: `Successfully created ${newClient.name}.`,
      })
    },
    onError: (error) => {
      const errorMessage = handleClientError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to create client',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to update an existing client
 */
export const useUpdateClient = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateClientRequest }) =>
      updateClient(id, data),
    onSuccess: (updatedClient) => {
      // Update the client in the cache
      queryClient.setQueryData(clientKeys.detail(updatedClient.id), updatedClient)

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: clientKeys.lists() })
      queryClient.invalidateQueries({ queryKey: clientKeys.active() })

      toast({
        title: 'Client updated',
        description: `Successfully updated ${updatedClient.name}.`,
      })
    },
    onError: (error) => {
      const errorMessage = handleClientError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to update client',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to delete a client
 */
export const useDeleteClient = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteClient,
    onSuccess: (_, deletedId) => {
      // Remove the client from the cache
      queryClient.removeQueries({ queryKey: clientKeys.detail(deletedId) })

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: clientKeys.lists() })
      queryClient.invalidateQueries({ queryKey: clientKeys.active() })

      toast({
        title: 'Client deleted',
        description: 'Successfully deleted the client.',
      })
    },
    onError: (error) => {
      const errorMessage = handleClientError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to delete client',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to toggle client status
 */
export const useToggleClientStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
      toggleClientStatus(id, isActive),
    onMutate: async ({ id, isActive }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: clientKeys.detail(id) })

      // Snapshot the previous value
      const previousClient = queryClient.getQueryData(clientKeys.detail(id))

      // Optimistically update to the new value
      if (previousClient) {
        queryClient.setQueryData(clientKeys.detail(id), {
          ...previousClient as Client,
          isActive,
        })
      }

      // Return a context object with the snapshotted value
      return { previousClient }
    },
    onSuccess: (updatedClient) => {
      // Update the client in the cache with server response
      queryClient.setQueryData(clientKeys.detail(updatedClient.id), updatedClient)

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: clientKeys.lists() })
      queryClient.invalidateQueries({ queryKey: clientKeys.active() })

      toast({
        title: 'Client status updated',
        description: `Client ${updatedClient.isActive ? 'activated' : 'deactivated'} successfully.`,
      })
    },
    onError: (error, { id }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousClient) {
        queryClient.setQueryData(clientKeys.detail(id), context.previousClient)
      }

      const errorMessage = handleClientError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to update client status',
        description: errorMessage,
      })
    },
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: clientKeys.detail(id) })
    },
  })
}

/**
 * Hook to get clients with real-time updates
 */
export const useClientsWithRefresh = (refetchInterval?: number) => {
  return useQuery({
    queryKey: clientKeys.lists(),
    queryFn: getAllClients,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: refetchInterval || false,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  })
}

/**
 * Hook to prefetch a client's details
 */
export const usePrefetchClient = () => {
  const queryClient = useQueryClient()

  return (id: number) => {
    queryClient.prefetchQuery({
      queryKey: clientKeys.detail(id),
      queryFn: () => getClientById(id),
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }
}
