import { z } from 'zod'

// Consultant status schema
const consultantStatusSchema = z.union([
  z.literal('active'),
  z.literal('inactive'),
  z.literal('pending'),
  z.literal('suspended'),
])
export type ConsultantStatus = z.infer<typeof consultantStatusSchema>

// API-compliant consultant schema matching the backend structure
export const consultantSchema = z.object({
  id: z.number(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  phoneNumber: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  dateOfBirth: z.string().optional(),
  nationality: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  bankAccountNumber: z.string().optional(),
  socialSecurityNumber: z.string().optional(),
  taxNumber: z.string().optional(),
  contractStartDate: z.string().optional(),
  contractEndDate: z.string().optional(),
  hourlyRate: z.number().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

// Form schemas for creating/editing consultants
export const consultantFormSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Please confirm your password'),
  isAdmin: z.boolean().default(false),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// List schema for parsing arrays of consultants
export const consultantListSchema = z.array(consultantSchema)

// Type exports
export type Consultant = z.infer<typeof consultantSchema>
export type ConsultantForm = z.infer<typeof consultantFormSchema>
export type ConsultantList = z.infer<typeof consultantListSchema>

// API request/response types
export interface CreateConsultantRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  phone?: string
  isAdmin: boolean
}

export interface UpdateConsultantRequest extends Partial<CreateConsultantRequest> {
  id: number
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  error: string
  success: boolean
  message?: string
}

// Helper types for UI components
export interface ConsultantTableRow extends Consultant {
  fullName: string
  status: ConsultantStatus
}

// Validation helpers
export const getConsultantStatus = (consultant: Consultant): ConsultantStatus => {
  if (!consultant.isActive) return 'inactive'
  if (consultant.contractEndDate && new Date(consultant.contractEndDate) < new Date()) return 'inactive'
  if (consultant.contractStartDate && new Date(consultant.contractStartDate) > new Date()) return 'pending'
  return 'active'
}

export const getConsultantFullName = (consultant: Consultant): string => {
  return `${consultant.firstName} ${consultant.lastName}`
}
