import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/hooks/use-toast'
import {
  getAllConsultants,
  getConsultantById,
  getCurrentConsultant,
  createConsultant,
  updateConsultant,
  deleteConsultant,
  getActiveConsultants,
  searchConsultants,
  toggleConsultantStatus,
  handleConsultantError,
} from '@/services/consultants'
import type {
  Consultant,
  CreateConsultantRequest,
  UpdateConsultantRequest,
} from '../data/schema'

// Query keys
export const consultantKeys = {
  all: ['consultants'] as const,
  lists: () => [...consultantKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...consultantKeys.lists(), { filters }] as const,
  details: () => [...consultantKeys.all, 'detail'] as const,
  detail: (id: number) => [...consultantKeys.details(), id] as const,
  current: () => [...consultantKeys.all, 'current'] as const,
  active: () => [...consultantKeys.all, 'active'] as const,
  search: (query: string) => [...consultantKeys.all, 'search', query] as const,
}

/**
 * Hook to get all consultants (admin only)
 */
export const useConsultants = () => {
  return useQuery({
    queryKey: consultantKeys.lists(),
    queryFn: getAllConsultants,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get a specific consultant by ID
 */
export const useConsultant = (id: number) => {
  return useQuery({
    queryKey: consultantKeys.detail(id),
    queryFn: () => getConsultantById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get current consultant's profile
 */
export const useCurrentConsultant = () => {
  return useQuery({
    queryKey: consultantKeys.current(),
    queryFn: getCurrentConsultant,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to get active consultants only
 */
export const useActiveConsultants = () => {
  return useQuery({
    queryKey: consultantKeys.active(),
    queryFn: getActiveConsultants,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to search consultants
 */
export const useSearchConsultants = (query: string) => {
  return useQuery({
    queryKey: consultantKeys.search(query),
    queryFn: () => searchConsultants(query),
    enabled: query.length >= 2, // Only search if query is at least 2 characters
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to create a new consultant (admin only)
 */
export const useCreateConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createConsultant,
    onSuccess: (newConsultant) => {
      // Invalidate and refetch consultants list
      queryClient.invalidateQueries({ queryKey: consultantKeys.lists() })
      queryClient.invalidateQueries({ queryKey: consultantKeys.active() })

      // Add the new consultant to the cache
      queryClient.setQueryData(consultantKeys.detail(newConsultant.id), newConsultant)

      toast({
        title: 'Consultant created',
        description: `Successfully created ${newConsultant.firstName} ${newConsultant.lastName}.`,
      })
    },
    onError: (error) => {
      const errorMessage = handleConsultantError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to create consultant',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to update an existing consultant
 */
export const useUpdateConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateConsultantRequest }) =>
      updateConsultant(id, data),
    onSuccess: (updatedConsultant) => {
      // Update the consultant in the cache
      queryClient.setQueryData(consultantKeys.detail(updatedConsultant.id), updatedConsultant)

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: consultantKeys.lists() })
      queryClient.invalidateQueries({ queryKey: consultantKeys.active() })
      queryClient.invalidateQueries({ queryKey: consultantKeys.current() })

      toast({
        title: 'Consultant updated',
        description: `Successfully updated ${updatedConsultant.firstName} ${updatedConsultant.lastName}.`,
      })
    },
    onError: (error) => {
      const errorMessage = handleConsultantError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to update consultant',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to delete a consultant (admin only)
 */
export const useDeleteConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteConsultant,
    onSuccess: (_, deletedId) => {
      // Remove the consultant from the cache
      queryClient.removeQueries({ queryKey: consultantKeys.detail(deletedId) })

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: consultantKeys.lists() })
      queryClient.invalidateQueries({ queryKey: consultantKeys.active() })

      toast({
        title: 'Consultant deleted',
        description: 'Successfully deleted the consultant.',
      })
    },
    onError: (error) => {
      const errorMessage = handleConsultantError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to delete consultant',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to toggle consultant status
 */
export const useToggleConsultantStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
      toggleConsultantStatus(id, isActive),
    onSuccess: (updatedConsultant) => {
      // Update the consultant in the cache
      queryClient.setQueryData(consultantKeys.detail(updatedConsultant.id), updatedConsultant)

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: consultantKeys.lists() })
      queryClient.invalidateQueries({ queryKey: consultantKeys.active() })

      const status = updatedConsultant.isActive ? 'activated' : 'deactivated'
      toast({
        title: `Consultant ${status}`,
        description: `Successfully ${status} ${updatedConsultant.firstName} ${updatedConsultant.lastName}.`,
      })
    },
    onError: (error) => {
      const errorMessage = handleConsultantError(error)
      toast({
        variant: 'destructive',
        title: 'Failed to update consultant status',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to get consultants with real-time updates
 */
export const useConsultantsWithRefresh = (refetchInterval?: number) => {
  return useQuery({
    queryKey: consultantKeys.lists(),
    queryFn: getAllConsultants,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: refetchInterval || false,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  })
}

/**
 * Hook to prefetch a consultant's details
 */
export const usePrefetchConsultant = () => {
  const queryClient = useQueryClient()

  return (id: number) => {
    queryClient.prefetchQuery({
      queryKey: consultantKeys.detail(id),
      queryFn: () => getConsultantById(id),
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }
}
