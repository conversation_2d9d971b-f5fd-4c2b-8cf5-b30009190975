import { Calendar, Clock, AlertCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { mockNonWorkingDays } from '@/features/non-working-days/data/mock-data'
import { reasonOptions } from '@/features/non-working-days/data/schema'

export function LeavesOverview() {
  // Get current date for comparison
  const today = new Date()
  const currentYear = today.getFullYear()

  // Filter leaves for current year
  const currentYearLeaves = mockNonWorkingDays.filter(leave => {
    const startDate = new Date(leave.startDate)
    return startDate.getFullYear() === currentYear
  })

  // Calculate total days taken this year
  const totalDaysTaken = currentYearLeaves.reduce((total, leave) => {
    const startDate = new Date(leave.startDate)
    const endDate = new Date(leave.endDate)
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
    return total + diffDays
  }, 0)

  // Get upcoming leaves (next 3 months)
  const upcomingLeaves = mockNonWorkingDays.filter(leave => {
    const startDate = new Date(leave.startDate)
    const threeMonthsFromNow = new Date()
    threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3)
    return startDate >= today && startDate <= threeMonthsFromNow
  })

  // Get recent leaves (last 3 months)
  const recentLeaves = mockNonWorkingDays.filter(leave => {
    const startDate = new Date(leave.startDate)
    const threeMonthsAgo = new Date()
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)
    return startDate >= threeMonthsAgo && startDate < today
  }).slice(0, 3)

  // Get reason label
  const getReasonLabel = (reason: string) => {
    const option = reasonOptions.find(opt => opt.value === reason)
    return option ? option.label : reason
  }

  // Calculate days for a leave period
  const calculateDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  }

  return (
    <div className='space-y-4'>
      {/* Summary Stats */}
      <div className='grid grid-cols-2 gap-4'>
        <div className='rounded-lg border p-3 text-center'>
          <div className='flex items-center justify-center gap-2 mb-1'>
            <Calendar className='h-4 w-4 text-blue-600' />
            <span className='text-sm font-medium'>Days Taken</span>
          </div>
          <p className='text-2xl font-bold'>{totalDaysTaken}</p>
          <p className='text-xs text-muted-foreground'>This year</p>
        </div>
        <div className='rounded-lg border p-3 text-center'>
          <div className='flex items-center justify-center gap-2 mb-1'>
            <Clock className='h-4 w-4 text-green-600' />
            <span className='text-sm font-medium'>Upcoming</span>
          </div>
          <p className='text-2xl font-bold'>{upcomingLeaves.length}</p>
          <p className='text-xs text-muted-foreground'>Next 3 months</p>
        </div>
      </div>

      {/* Upcoming Leaves */}
      {upcomingLeaves.length > 0 && (
        <div className='space-y-3'>
          <h4 className='font-medium flex items-center gap-2'>
            <AlertCircle className='h-4 w-4 text-orange-500' />
            Upcoming Leaves
          </h4>
          <div className='space-y-2'>
            {upcomingLeaves.slice(0, 3).map((leave) => (
              <div key={leave.id} className='flex items-center justify-between rounded-lg border p-3'>
                <div className='space-y-1'>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline' className='text-xs'>
                      {getReasonLabel(leave.reason)}
                    </Badge>
                    <span className='text-sm font-medium'>
                      {calculateDays(leave.startDate, leave.endDate)} day{calculateDays(leave.startDate, leave.endDate) > 1 ? 's' : ''}
                    </span>
                  </div>
                  <p className='text-xs text-muted-foreground'>
                    {new Date(leave.startDate).toLocaleDateString()}
                    {leave.startDate !== leave.endDate && ` - ${new Date(leave.endDate).toLocaleDateString()}`}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Leaves */}
      {recentLeaves.length > 0 && (
        <div className='space-y-3'>
          <h4 className='font-medium'>Recent Leaves</h4>
          <div className='space-y-2'>
            {recentLeaves.map((leave) => (
              <div key={leave.id} className='flex items-center justify-between text-sm'>
                <div className='flex items-center gap-2'>
                  <Badge variant='secondary' className='text-xs'>
                    {getReasonLabel(leave.reason)}
                  </Badge>
                  <span className='text-muted-foreground'>
                    {new Date(leave.startDate).toLocaleDateString()}
                  </span>
                </div>
                <span className='font-medium'>
                  {calculateDays(leave.startDate, leave.endDate)} day{calculateDays(leave.startDate, leave.endDate) > 1 ? 's' : ''}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No data state */}
      {upcomingLeaves.length === 0 && recentLeaves.length === 0 && (
        <div className='text-center py-6 text-muted-foreground'>
          <Calendar className='h-8 w-8 mx-auto mb-2 opacity-50' />
          <p className='text-sm'>No recent or upcoming leaves</p>
        </div>
      )}
    </div>
  )
}
