import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { SelectDropdown } from '@/components/select-dropdown'
import { DatePicker } from '@/components/ui/date-picker'
import { nonWorkingDayFormSchema, NonWorkingDayForm, NonWorkingDay, reasonOptions } from '../data/schema'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentItem?: NonWorkingDay
}

export function NonWorkingDayMutateDrawer({ open, onOpenChange, currentItem }: Props) {
  const isUpdate = !!currentItem

  const form = useForm<NonWorkingDayForm>({
    resolver: zodResolver(nonWorkingDayFormSchema),
    defaultValues: currentItem ? {
      startDate: currentItem.startDate,
      endDate: currentItem.endDate,
      reason: currentItem.reason,
      customReason: currentItem.customReason || '',
    } : {
      startDate: '',
      endDate: '',
      reason: 'paid_leave',
      customReason: '',
    },
  })

  const watchedReason = form.watch('reason')

  const onSubmit = (data: NonWorkingDayForm) => {
    // TODO: Implement actual API call
    console.log('Submitting non-working day:', data)

    // Close the drawer
    onOpenChange(false)

    // Reset form after a short delay to allow close animation
    setTimeout(() => {
      form.reset()
    }, 300)

    toast({
      title: isUpdate ? 'Non-working day updated' : 'Non-working day added',
      description: `Successfully ${isUpdate ? 'updated' : 'added'} non-working day.`,
    })
  }

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        onOpenChange(v)
        if (!v) form.reset()
      }}
    >
      <SheetContent className='flex flex-col'>
        <SheetHeader className='text-left'>
          <SheetTitle>{isUpdate ? 'Update' : 'Add'} Non-working Day</SheetTitle>
          <SheetDescription>
            {isUpdate
              ? 'Update the non-working day details below.'
              : 'Add a new non-working day to your calendar.'}
            Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form
            id='non-working-day-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='flex-1 space-y-6'
          >
            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='startDate'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={(date) => {
                          const dateString = date ? date.toISOString().split('T')[0] : ''
                          field.onChange(dateString)

                          // Auto-set end date to same as start date if not set
                          if (dateString && !form.getValues('endDate')) {
                            form.setValue('endDate', dateString)
                          }
                        }}
                        placeholder="Select start date"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='endDate'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={(date) => {
                          field.onChange(date ? date.toISOString().split('T')[0] : '')
                        }}
                        placeholder="Select end date"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='reason'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason</FormLabel>
                  <SelectDropdown
                    defaultValue={field.value}
                    onValueChange={(value) => {
                      field.onChange(value)
                      // Clear custom reason when changing away from 'other'
                      if (value !== 'other') {
                        form.setValue('customReason', '')
                      }
                    }}
                    placeholder='Select a reason'
                    items={reasonOptions.map(option => ({
                      label: option.label,
                      value: option.value,
                    }))}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            {watchedReason === 'other' && (
              <FormField
                control={form.control}
                name='customReason'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Reason</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Please specify the reason...'
                        className='resize-none'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </form>
        </Form>

        <SheetFooter className='gap-2'>
          <SheetClose asChild>
            <Button variant='outline'>Cancel</Button>
          </SheetClose>
          <Button form='non-working-day-form' type='submit'>
            {isUpdate ? 'Update' : 'Add'} Day
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
