import { toast } from '@/hooks/use-toast'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useTimesheet } from '../context/timesheet-context'
import { WorkTimePeriodDrawer } from './work-time-period-drawer'
import { WorkTimePeriodDetailsDialog } from './work-time-period-details-dialog'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteWorkTimePeriod, validateWorkTimePeriod } from '@/services/timesheet'

export function WorkTimePeriodDialogs() {
  const { 
    open, 
    setOpen, 
    currentPeriod, 
    setCurrentPeriod 
  } = useTimesheet()
  
  const queryClient = useQueryClient()

  const deleteMutation = useMutation({
    mutationFn: deleteWorkTimePeriod,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Work time period deleted successfully',
      })
      queryClient.invalidateQueries({ queryKey: ['workTimePeriods'] })
      setOpen(null)
      setCurrentPeriod(null)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete work time period',
        variant: 'destructive',
      })
    },
  })

  const validateMutation = useMutation({
    mutationFn: validateWorkTimePeriod,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Work time period validated successfully',
      })
      queryClient.invalidateQueries({ queryKey: ['workTimePeriods'] })
      setOpen(null)
      setCurrentPeriod(null)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to validate work time period',
        variant: 'destructive',
      })
    },
  })

  const handleDelete = () => {
    if (currentPeriod) {
      deleteMutation.mutate(currentPeriod.id)
    }
  }

  const handleValidate = () => {
    if (currentPeriod) {
      validateMutation.mutate(currentPeriod.id)
    }
  }

  return (
    <>
      {/* Create/Update Work Time Period */}
      <WorkTimePeriodDrawer
        open={open === 'create-period' || open === 'update-period'}
        onOpenChange={(isOpen) => {
          setOpen(isOpen ? open : null)
          if (!isOpen) {
            setTimeout(() => {
              setCurrentPeriod(null)
            }, 500)
          }
        }}
      />

      {/* View Work Time Period Details */}
      {currentPeriod && (
        <WorkTimePeriodDetailsDialog
          open={open === 'view-period'}
          onOpenChange={(isOpen) => {
            setOpen(isOpen ? 'view-period' : null)
            if (!isOpen) {
              setTimeout(() => {
                setCurrentPeriod(null)
              }, 500)
            }
          }}
          period={currentPeriod}
        />
      )}

      {/* Delete Work Time Period */}
      <ConfirmDialog
        open={open === 'delete-period'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'delete-period' : null)}
        title="Delete Work Time Period"
        description={
          currentPeriod
            ? `Are you sure you want to delete the work time period from ${new Date(currentPeriod.startDate).toLocaleDateString()} to ${new Date(currentPeriod.endDate).toLocaleDateString()}? This action cannot be undone.`
            : 'Are you sure you want to delete this work time period?'
        }
        onConfirm={handleDelete}
        loading={deleteMutation.isPending}
        variant="destructive"
      />

      {/* Validate Work Time Period */}
      <ConfirmDialog
        open={open === 'validate-period'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'validate-period' : null)}
        title="Validate Work Time Period"
        description={
          currentPeriod
            ? `Are you sure you want to validate the work time period from ${new Date(currentPeriod.startDate).toLocaleDateString()} to ${new Date(currentPeriod.endDate).toLocaleDateString()}? Once validated, it cannot be modified.`
            : 'Are you sure you want to validate this work time period?'
        }
        onConfirm={handleValidate}
        loading={validateMutation.isPending}
        confirmText="Validate"
      />
    </>
  )
}
