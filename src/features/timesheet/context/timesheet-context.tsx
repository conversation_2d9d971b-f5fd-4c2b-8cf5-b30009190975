import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { WorkTimePeriod, TimeEntry, Client } from '../data/schema'

type TimesheetDialogType = 'create-period' | 'update-period' | 'delete-period' | 'view-period' | 'validate-period' | 'import'

interface TimesheetContextType {
  open: TimesheetDialogType | null
  setOpen: (str: TimesheetDialogType | null) => void
  currentPeriod: WorkTimePeriod | null
  setCurrentPeriod: React.Dispatch<React.SetStateAction<WorkTimePeriod | null>>
  selectedPeriods: WorkTimePeriod[]
  setSelectedPeriods: React.Dispatch<React.SetStateAction<WorkTimePeriod[]>>
  clients: Client[]
  setClients: React.Dispatch<React.SetStateAction<Client[]>>
  isLoading: boolean
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
  error: string | null
  setError: React.Dispatch<React.SetStateAction<string | null>>
  // Legacy support for backward compatibility
  currentRow: TimeEntry | null
  setCurrentRow: React.Dispatch<React.SetStateAction<TimeEntry | null>>
}

const TimesheetContext = React.createContext<TimesheetContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function TimesheetProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<TimesheetDialogType>(null)
  const [currentPeriod, setCurrentPeriod] = useState<WorkTimePeriod | null>(null)
  const [selectedPeriods, setSelectedPeriods] = useState<WorkTimePeriod[]>([])
  const [clients, setClients] = useState<Client[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Legacy support
  const [currentRow, setCurrentRow] = useState<TimeEntry | null>(null)

  return (
    <TimesheetContext value={{
      open,
      setOpen,
      currentPeriod,
      setCurrentPeriod,
      selectedPeriods,
      setSelectedPeriods,
      clients,
      setClients,
      isLoading,
      setIsLoading,
      error,
      setError,
      // Legacy support
      currentRow,
      setCurrentRow
    }}>
      {children}
    </TimesheetContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useTimesheet = () => {
  const timesheetContext = React.useContext(TimesheetContext)

  if (!timesheetContext) {
    throw new Error('useTimesheet has to be used within <TimesheetContext>')
  }

  return timesheetContext
}
