import { TimeEntry, Client, Project, WorkTimePeriod } from './schema'

// API-compliant mock clients
export const mockApiClients: Client[] = [
  {
    id: 1,
    name: 'Acme Corporation',
    code: 'ACME',
  },
  {
    id: 2,
    name: 'TechStart Inc.',
    code: 'TECH',
  },
  {
    id: 3,
    name: 'Global Solutions Ltd.',
    code: 'GLOB',
  },
  {
    id: 4,
    name: 'Innovation Labs',
    code: 'INNO',
  },
]

// Legacy mock clients for backward compatibility
export const mockClients: Client[] = [
  {
    id: '1',
    name: 'Acme Corporation',
    code: 'ACME',
  },
  {
    id: '2',
    name: 'TechStart Inc.',
    code: 'TECH',
  },
  {
    id: '3',
    name: 'Global Solutions Ltd.',
    code: 'GLOB',
  },
  {
    id: '4',
    name: 'Innovation Labs',
    code: 'INNO',
  },
]

export const mockProjects: Project[] = [
  {
    id: '1',
    name: 'Website Redesign',
    clientId: '1',
    code: 'WEB-001',
  },
  {
    id: '2',
    name: 'Mobile App Development',
    clientId: '1',
    code: 'MOB-001',
  },
  {
    id: '3',
    name: 'Data Migration',
    clientId: '2',
    code: 'DATA-001',
  },
  {
    id: '4',
    name: 'Security Audit',
    clientId: '2',
    code: 'SEC-001',
  },
  {
    id: '5',
    name: 'Cloud Infrastructure',
    clientId: '3',
    code: 'CLOUD-001',
  },
  {
    id: '6',
    name: 'AI Research',
    clientId: '4',
    code: 'AI-001',
  },
]

export const mockTimeEntries: TimeEntry[] = [
  {
    id: '1',
    date: '2024-01-15',
    hours: 8,
    location: 'office',
    client: 'Acme Corporation',
    project: 'Website Redesign',
    description: 'Frontend development and UI improvements',
    consultantId: 'consultant-1',
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z',
  },
  {
    id: '2',
    date: '2024-01-16',
    hours: 7.5,
    location: 'home',
    client: 'TechStart Inc.',
    project: 'Data Migration',
    description: 'Database schema design and migration scripts',
    consultantId: 'consultant-1',
    createdAt: '2024-01-16T08:30:00Z',
    updatedAt: '2024-01-16T08:30:00Z',
  },
  {
    id: '3',
    date: '2024-01-17',
    hours: 8,
    location: 'office',
    client: 'Global Solutions Ltd.',
    project: 'Cloud Infrastructure',
    description: 'AWS setup and configuration',
    consultantId: 'consultant-1',
    createdAt: '2024-01-17T09:15:00Z',
    updatedAt: '2024-01-17T09:15:00Z',
  },
  {
    id: '4',
    date: '2024-01-18',
    hours: 6,
    location: 'home',
    client: 'Innovation Labs',
    project: 'AI Research',
    description: 'Machine learning model development',
    consultantId: 'consultant-1',
    createdAt: '2024-01-18T10:00:00Z',
    updatedAt: '2024-01-18T10:00:00Z',
  },
  {
    id: '5',
    date: '2024-01-19',
    hours: 8.5,
    location: 'office',
    client: 'Acme Corporation',
    project: 'Mobile App Development',
    description: 'React Native development and testing',
    consultantId: 'consultant-1',
    createdAt: '2024-01-19T08:45:00Z',
    updatedAt: '2024-01-19T08:45:00Z',
  },
]

// Mock work time periods for API-compliant interface
export const mockWorkTimePeriods: WorkTimePeriod[] = [
  {
    id: 1,
    startDate: '2024-01-15',
    endDate: '2024-01-19',
    totalHours: 38,
    remoteWorkPercentage: 40,
    notes: 'Productive week with mixed remote and office work',
    isValidated: true,
    validationStatus: 'Approved',
    periodIdentifier: '2024-01-15 to 2024-01-19',
    durationInDays: 5,
    activities: [
      {
        id: 1,
        activityName: 'Frontend Development',
        hours: 16,
        description: 'React component development and UI improvements',
        isBillable: true,
        percentageOfTotal: 42.1,
        client: mockApiClients[0], // Acme Corporation
      },
      {
        id: 2,
        activityName: 'Database Migration',
        hours: 12,
        description: 'Schema design and data migration scripts',
        isBillable: true,
        percentageOfTotal: 31.6,
        client: mockApiClients[1], // TechStart Inc.
      },
      {
        id: 3,
        activityName: 'Team Meetings',
        hours: 6,
        description: 'Daily standups and planning sessions',
        isBillable: false,
        percentageOfTotal: 15.8,
      },
      {
        id: 4,
        activityName: 'Documentation',
        hours: 4,
        description: 'Technical documentation and code reviews',
        isBillable: false,
        percentageOfTotal: 10.5,
      },
    ],
    consultant: {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
  },
  {
    id: 2,
    startDate: '2024-01-22',
    endDate: '2024-01-26',
    totalHours: 40,
    remoteWorkPercentage: 75,
    notes: 'Mostly remote work week focusing on cloud infrastructure',
    isValidated: false,
    validationStatus: 'Pending Review',
    periodIdentifier: '2024-01-22 to 2024-01-26',
    durationInDays: 5,
    activities: [
      {
        id: 5,
        activityName: 'Cloud Infrastructure Setup',
        hours: 24,
        description: 'AWS configuration and deployment automation',
        isBillable: true,
        percentageOfTotal: 60.0,
        client: mockApiClients[2], // Global Solutions Ltd.
      },
      {
        id: 6,
        activityName: 'AI Model Development',
        hours: 12,
        description: 'Machine learning model training and optimization',
        isBillable: true,
        percentageOfTotal: 30.0,
        client: mockApiClients[3], // Innovation Labs
      },
      {
        id: 7,
        activityName: 'Research & Learning',
        hours: 4,
        description: 'Staying up-to-date with latest technologies',
        isBillable: false,
        percentageOfTotal: 10.0,
      },
    ],
    consultant: {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
  },
  {
    id: 3,
    startDate: '2024-01-29',
    endDate: '2024-01-29',
    totalHours: 8,
    remoteWorkPercentage: 0,
    notes: 'Single day intensive workshop at client site',
    isValidated: false,
    validationStatus: 'Draft',
    periodIdentifier: '2024-01-29',
    durationInDays: 1,
    activities: [
      {
        id: 8,
        activityName: 'Client Workshop',
        hours: 6,
        description: 'Requirements gathering and system design workshop',
        isBillable: true,
        percentageOfTotal: 75.0,
        client: mockApiClients[0], // Acme Corporation
      },
      {
        id: 9,
        activityName: 'Travel Time',
        hours: 2,
        description: 'Travel to and from client site',
        isBillable: false,
        percentageOfTotal: 25.0,
      },
    ],
    consultant: {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
  },
]