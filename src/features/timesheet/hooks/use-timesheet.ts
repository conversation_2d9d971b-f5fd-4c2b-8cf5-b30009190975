import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/hooks/use-toast'
import {
  getAllWorkTimePeriods,
  getWorkTimePeriod,
  createWorkTimePeriod,
  updateWorkTimePeriod,
  deleteWorkTimePeriod,
  getConsultantWorkTimePeriods,
  getConsultantWorkTimePeriodsInRange,
  getWorkTimeStatistics,
  validateWorkTimePeriod,
  getClients,
  getConsultantInfo,
  calculateWorkTimeStats,
} from '@/services/timesheet'
import type {
  WorkTimePeriod,
  CreateWorkTimePeriodRequest,
  UpdateWorkTimePeriodRequest,
  WorkTimeStatistics,
  Client,
  ConsultantInfo,
} from '@/features/timesheet/types'

// Query keys factory
export const timesheetKeys = {
  all: ['timesheet'] as const,
  workTimePeriods: () => [...timesheetKeys.all, 'workTimePeriods'] as const,
  workTimePeriod: (id: number) => [...timesheetKeys.workTimePeriods(), id] as const,
  consultantPeriods: (consultantId: number) => [...timesheetKeys.workTimePeriods(), 'consultant', consultantId] as const,
  consultantPeriodsInRange: (consultantId: number, startDate: string, endDate: string) => 
    [...timesheetKeys.consultantPeriods(consultantId), 'range', startDate, endDate] as const,
  statistics: (consultantId: number) => [...timesheetKeys.all, 'statistics', consultantId] as const,
  clients: () => [...timesheetKeys.all, 'clients'] as const,
}

/**
 * Hook to get all work time periods (admin only)
 */
export const useWorkTimePeriods = () => {
  return useQuery({
    queryKey: timesheetKeys.workTimePeriods(),
    queryFn: getAllWorkTimePeriods,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get a specific work time period by ID
 */
export const useWorkTimePeriod = (id: number) => {
  return useQuery({
    queryKey: timesheetKeys.workTimePeriod(id),
    queryFn: () => getWorkTimePeriod(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get work time periods for a specific consultant
 */
export const useConsultantWorkTimePeriods = (consultantId: number) => {
  return useQuery({
    queryKey: timesheetKeys.consultantPeriods(consultantId),
    queryFn: () => getConsultantWorkTimePeriods(consultantId),
    enabled: !!consultantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to get work time periods for a consultant within a date range
 */
export const useConsultantWorkTimePeriodsInRange = (
  consultantId: number,
  startDate: string,
  endDate: string,
  enabled = true
) => {
  return useQuery({
    queryKey: timesheetKeys.consultantPeriodsInRange(consultantId, startDate, endDate),
    queryFn: () => getConsultantWorkTimePeriodsInRange(consultantId, startDate, endDate),
    enabled: enabled && !!consultantId && !!startDate && !!endDate,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to get work time statistics for a consultant
 */
export const useWorkTimeStatistics = (consultantId: number) => {
  return useQuery({
    queryKey: timesheetKeys.statistics(consultantId),
    queryFn: () => getWorkTimeStatistics(consultantId),
    enabled: !!consultantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get clients for dropdown/selection
 */
export const useClients = () => {
  return useQuery({
    queryKey: timesheetKeys.clients(),
    queryFn: getClients,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to get consultant info from localStorage
 */
export const useConsultantInfo = () => {
  return useQuery({
    queryKey: ['consultantInfo'],
    queryFn: () => {
      const info = getConsultantInfo()
      if (!info) {
        throw new Error('Consultant information not found')
      }
      return info
    },
    staleTime: Infinity, // Never stale since it's from localStorage
    retry: false, // Don't retry if not found
  })
}

/**
 * Hook to create a new work time period
 */
export const useCreateWorkTimePeriod = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createWorkTimePeriod,
    onSuccess: (newPeriod) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: timesheetKeys.workTimePeriods() })
      queryClient.invalidateQueries({ queryKey: timesheetKeys.consultantPeriods(newPeriod.consultant.id) })
      queryClient.invalidateQueries({ queryKey: timesheetKeys.statistics(newPeriod.consultant.id) })
      
      // Add the new period to the cache
      queryClient.setQueryData(timesheetKeys.workTimePeriod(newPeriod.id), newPeriod)

      toast({
        title: 'Work period created',
        description: `Successfully created work period from ${newPeriod.startDate} to ${newPeriod.endDate}.`,
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to create work period'
      toast({
        variant: 'destructive',
        title: 'Failed to create work period',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to update an existing work time period
 */
export const useUpdateWorkTimePeriod = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateWorkTimePeriod,
    onSuccess: (updatedPeriod) => {
      // Update the period in the cache
      queryClient.setQueryData(timesheetKeys.workTimePeriod(updatedPeriod.id), updatedPeriod)
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: timesheetKeys.workTimePeriods() })
      queryClient.invalidateQueries({ queryKey: timesheetKeys.consultantPeriods(updatedPeriod.consultant.id) })
      queryClient.invalidateQueries({ queryKey: timesheetKeys.statistics(updatedPeriod.consultant.id) })

      toast({
        title: 'Work period updated',
        description: `Successfully updated work period from ${updatedPeriod.startDate} to ${updatedPeriod.endDate}.`,
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update work period'
      toast({
        variant: 'destructive',
        title: 'Failed to update work period',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to delete a work time period
 */
export const useDeleteWorkTimePeriod = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteWorkTimePeriod,
    onSuccess: (_, deletedId) => {
      // Remove the period from the cache
      queryClient.removeQueries({ queryKey: timesheetKeys.workTimePeriod(deletedId) })
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: timesheetKeys.workTimePeriods() })
      queryClient.invalidateQueries({ queryKey: timesheetKeys.all })

      toast({
        title: 'Work period deleted',
        description: 'Successfully deleted the work period.',
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to delete work period'
      toast({
        variant: 'destructive',
        title: 'Failed to delete work period',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to validate a work time period (admin only)
 */
export const useValidateWorkTimePeriod = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: validateWorkTimePeriod,
    onSuccess: (validatedPeriod) => {
      // Update the period in the cache
      queryClient.setQueryData(timesheetKeys.workTimePeriod(validatedPeriod.id), validatedPeriod)
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: timesheetKeys.workTimePeriods() })
      queryClient.invalidateQueries({ queryKey: timesheetKeys.consultantPeriods(validatedPeriod.consultant.id) })

      toast({
        title: 'Work period validated',
        description: `Successfully validated work period from ${validatedPeriod.startDate} to ${validatedPeriod.endDate}.`,
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to validate work period'
      toast({
        variant: 'destructive',
        title: 'Failed to validate work period',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to calculate statistics from periods (client-side calculation)
 */
export const useCalculatedWorkTimeStats = (periods: WorkTimePeriod[] | undefined) => {
  return useQuery({
    queryKey: ['calculatedStats', periods?.length],
    queryFn: () => calculateWorkTimeStats(periods || []),
    enabled: !!periods,
    staleTime: Infinity, // Never stale since it's calculated from existing data
  })
}
