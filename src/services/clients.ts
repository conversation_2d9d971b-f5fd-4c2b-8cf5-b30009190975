import { apiClient } from '@/lib/api'
import type {
  Client,
  CreateClientRequest,
  UpdateClientRequest,
  ApiResponse,
} from '@/features/clients/data/schema'

/**
 * Get all clients
 * @returns Promise<Client[]>
 */
export const getAllClients = async (): Promise<Client[]> => {
  const response = await apiClient.get<Client[]>('/v1/clients')
  return response.data
}

/**
 * Get a specific client by ID
 * @param id - Client ID
 * @returns Promise<Client>
 */
export const getClientById = async (id: number): Promise<Client> => {
  const response = await apiClient.get<Client>(`/v1/clients/${id}`)
  return response.data
}

/**
 * Create a new client (admin only)
 * @param clientData - Client data to create
 * @returns Promise<Client>
 */
export const createClient = async (clientData: CreateClientRequest): Promise<Client> => {
  const response = await apiClient.post<Client>('/v1/clients', clientData)
  return response.data
}

/**
 * Update an existing client (admin only)
 * @param id - Client ID
 * @param clientData - Updated client data
 * @returns Promise<Client>
 */
export const updateClient = async (id: number, clientData: UpdateClientRequest): Promise<Client> => {
  const response = await apiClient.put<Client>(`/v1/clients/${id}`, clientData)
  return response.data
}

/**
 * Delete a client (admin only)
 * @param id - Client ID
 * @returns Promise<void>
 */
export const deleteClient = async (id: number): Promise<void> => {
  await apiClient.delete(`/v1/clients/${id}`)
}

/**
 * Get active clients only (for dropdowns/selections)
 * @returns Promise<Client[]>
 */
export const getActiveClients = async (): Promise<Client[]> => {
  const allClients = await getAllClients()
  return allClients.filter(client => client.isActive)
}

/**
 * Search clients by name or code
 * @param query - Search query
 * @returns Promise<Client[]>
 */
export const searchClients = async (query: string): Promise<Client[]> => {
  const response = await apiClient.get<Client[]>(`/v1/clients/search?q=${encodeURIComponent(query)}`)
  return response.data
}

/**
 * Toggle client active status (admin only)
 * @param id - Client ID
 * @param isActive - New active status
 * @returns Promise<Client>
 */
export const toggleClientStatus = async (id: number, isActive: boolean): Promise<Client> => {
  return updateClient(id, { isActive })
}

// Error handling utilities
export const handleClientError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message
  }

  if (error.response?.status === 404) {
    return 'Client not found'
  }

  if (error.response?.status === 403) {
    return 'You do not have permission to perform this action'
  }

  if (error.response?.status === 409) {
    return 'A client with this code already exists'
  }

  if (error.message) {
    return error.message
  }

  return 'An unexpected error occurred'
}

// Type guards and validation
export const isValidClientCode = (code: string): boolean => {
  return /^[A-Z0-9]{2,10}$/.test(code)
}

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
